package life.ljs.hg.regulations.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ChooseType {
    ALL(0, "所有"),
    EXTERNAL(1, "外规"),
    INNER(2, "内规"),
    INNER_HEAD(3, "内规总行"),
    INNER_BRANCH(4, "内规分行"),
    EXTERNAL_INNER_HEAD(5, "外规和内规总行"),
    EXTERNAL_INNER_BRANCH(6,"外规和内规分行");

    private final int code;
    private final String value;

    public static ChooseType getChooseType(int code){
        for (ChooseType chooseType : ChooseType.values()) {
            if(chooseType.getCode() == code){
                return chooseType;
            }
        }
        return null;
    }
}
