package life.ljs.hg.regulations.constants;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/7/22
 **/
public class RegulationConstant {
    public static final String INVALID = "失效";
    public static final String SPACE = " ";
    public static final String yearCN = "年";
    public static final String monthCN = "月";
    public static final String yearPrefix = "20";
    public static final String monthPrefix = "0";
    public static final String dateSeparator = "-";

    public static final String regPostDateStartFieldName = "regulationPostDateStart";
    public static final String regPostDateEndFieldName = "regulationPostDateEnd";
    public static final String regInnerControlDepartFieldName = "regInnerControlDept";

    public static final String regLabelsFieldName = "regLabels";
    public static final String regOutPostDeptFieldName = "outPostDepartment";
    public static final String regNumberFieldName = "regNumber";
    public static final String regContentFieldName = "regContent";
    public static final String regTitleFieldName = "regTitle";
    public static final String regTypeFieldName = "regType";
    public static final String ybjName = "国家金融监督管理总局（原银保监会）";

    public static final String executeDateMinAggName = "executeDateMinAgg";
    public static final String executeDateMaxAggName = "executeDateMaxAgg";
    public static final String validLevelAggName = "validLevelAgg";
    public static final String postDepartmentAggName = "regulationDepartmentAgg";
    public static final String innerRegulationTypeAggName = "innerRegTypeAgg";
    public static final String innerRegulationControlDepartAggName = "innerRegulationControlDepartAgg";
}
