package life.ljs.hg.regulations.service;

import cn.hutool.core.util.StrUtil;
import life.ljs.hg.regulations.constants.RegulationConstant;
import life.ljs.hg.regulations.dto.LabelDto;
import life.ljs.hg.regulations.parser.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/7/24
 **/
public class SearchWordDealCenter {
    public static Map<String,Object> parseSearchParam(String searchContent){
        Map<String,Object> result = new HashMap<>();

        if(StrUtil.isBlankIfStr(searchContent)){
            return result;
        }

        String[] searchWords = searchContent.split(RegulationConstant.SPACE);

        DateTimeIdentify dateTimeIdentify = new DateTimeIdentify();
        LabelIdentify labelIdentify = new LabelIdentify();
        PostDepartmentIdentity postDepartmentIdentity = new PostDepartmentIdentity();
        RegNumberIdentify regNumberIdentify = new RegNumberIdentify();
        RegTypeIdentity regTypeIdentity = new RegTypeIdentity();
        RegTitleIdentity regTitleIdentity = new RegTitleIdentity();

        Map<String, Object> dateResult = dateTimeIdentify.identity(searchWords);
        Map<String, Object> labelResult = labelIdentify.identity(searchWords);
        Map<String, Object> postDeptResult = postDepartmentIdentity.identity(searchWords);
        Map<String, Object> regNumberResult = regNumberIdentify.identity(searchWords);
        Map<String, Object> regTypeResult = regTypeIdentity.identity(searchWords);
        Map<String, Object> identity = regTitleIdentity.identity(searchWords);

        if(dateResult != null){
            result.putAll(dateResult);
        }
        List<LabelDto> labelDtos = null;
        if(labelResult != null){
            result.putAll(labelResult);
            labelDtos = (List<LabelDto>)labelResult.get(RegulationConstant.regLabelsFieldName);
        }
        if (postDeptResult != null){
            result.putAll(postDeptResult);
        }
        if(regNumberResult != null){
            result.putAll(regNumberResult);
        }
        if(regTypeResult != null){
            result.putAll(regTypeResult);
        }
        if(!identity.isEmpty()){
            result.putAll(identity);
            //标题与标签冲突的去除
            if(identity.containsKey(RegulationConstant.regTitleFieldName)){
                if(labelDtos != null && labelDtos.size() > 0){
                    List<String>list = (List<String>)identity.get(RegulationConstant.regTitleFieldName);
                    List<String> tobeDel = new ArrayList<>();
                    for (String s : list) {
                        boolean b = labelDtos.stream().anyMatch(labelDto -> (labelDto.getSynonyms()!=null && (labelDto.getSynonyms().contains(s)
                        ||labelDto.getLabelName().equals(s))));
                        if(b){
                            tobeDel.add(s);
                        }
                    }
                    if(tobeDel.size() > 0){
                        list.removeAll(tobeDel);
                    }
                    if(list.size() == 0){
                        identity.remove(RegulationConstant.regTitleFieldName);
                    }
                }
            }
        }
        if (result.isEmpty()){
            result.put(RegulationConstant.regContentFieldName, searchContent);
        }
        return result;
    }
}
