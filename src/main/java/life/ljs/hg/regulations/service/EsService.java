package life.ljs.hg.regulations.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import life.ljs.hg.regulations.constants.ChooseType;
import life.ljs.hg.regulations.constants.RegulationConstant;
import life.ljs.hg.regulations.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.DisMaxQueryBuilder;
import org.elasticsearch.index.query.MatchPhrasePrefixQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.IncludeExclude;
import org.elasticsearch.search.aggregations.metrics.MaxAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.MinAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.sort.ScriptSortBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/7/21
 **/
@Service
@Slf4j
public class EsService {
    @Value("${es.index.regulation}")
    private String INDEX_REGULATION;
    @Value("${es.index.hotword.suggest}")
    private  String INDEX_HOTWORD_SUGGEST;
    @Value("${es.index.hotword.suggest.field}")
    private String HOTWORD_SUGGEST_FIELD ;
    @Value("${es.min.score}")
    private float minScore;
    @Autowired
    private RestHighLevelClient client;

    /**
     * 搜索建议接口
     * @param word 检索词
     * @return
     */
    public List<String> suggestSearch(String word){
        SearchRequest searchRequest = new SearchRequest(INDEX_HOTWORD_SUGGEST);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        MatchPhrasePrefixQueryBuilder matchPhrasePrefixQueryBuilder =
                QueryBuilders.matchPhrasePrefixQuery(HOTWORD_SUGGEST_FIELD, word);
        searchSourceBuilder.query(matchPhrasePrefixQueryBuilder);
        searchRequest.source(searchSourceBuilder);
        List<String> result = new ArrayList<>();
        try {
            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits hits = response.getHits();
            long value = hits.getTotalHits().value;
            if (value > 0) {
                hits.forEach(hit -> {
                    result.add((String) hit.getSourceAsMap().get(HOTWORD_SUGGEST_FIELD));
                });
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return result;
    }

    public RegulationsAggResponse simpleSearch(RegulationsAggRequest request){
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        String [] fetchFields = {"id", "regulationTitle", "regulationNumber", "regulationPostDate",
        "regulationExecuteDate","regulationType","regulationLabels","regulationLaws","innerRegulationControlDepart"};
        searchSourceBuilder.fetchSource(fetchFields, null);
        Map<String,Object> map = createCommonQueryBuilder(request);
        BoolQueryBuilder builder = (BoolQueryBuilder) map.get("builder");
        RegulationsAggResponse regResponse = (RegulationsAggResponse) map.get("resp");
        List<String> highLightFields = (List<String>) map.get("high");
        searchSourceBuilder.query(builder);
        //补充排序信息
        setFromSize(searchSourceBuilder,request.getPageSize(),request.getPageNum());
        setSort(searchSourceBuilder,request.getSortObj(),regResponse,request.getSearchFilterParam());
        //补充高亮
        if(regResponse.getSearchType()!=-1){
            HighlightBuilder highlightBuilder = new HighlightBuilder();
            highlightBuilder.preTags("<high>");
            highlightBuilder.postTags("</high>");
            for (String highLightField : highLightFields) {
                HighlightBuilder.Field field = new HighlightBuilder.Field(highLightField);
                highlightBuilder.field(field);
            }
            searchSourceBuilder.highlighter(highlightBuilder);
        }
        //补充聚合查询
        for(AggregationBuilder agg:getAggs()){
            searchSourceBuilder.aggregation(agg);
        }
        searchSourceBuilder.trackTotalHits(true);
        if(ObjectUtil.isNotEmpty(map.get("minScore"))){
            searchSourceBuilder.minScore(minScore);
        }
        log.info("Query DSL:{}",searchSourceBuilder);
        //执行查询
        SearchRequest searchRequest = new SearchRequest(INDEX_REGULATION);
        searchRequest.source(searchSourceBuilder);
        try {
            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
            //解析es的结果集
//            parseResponse(response,regResponse);
            return regResponse;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private List<AggregationBuilder> getAggs() {
        List<AggregationBuilder> aggs = new ArrayList<>();
        MinAggregationBuilder regulationExecuteDateMinAgg = AggregationBuilders.min(RegulationConstant.executeDateMinAggName).field("regulationExecuteDate");
        MaxAggregationBuilder regulationExecuteDateMaxAgg = AggregationBuilders.max(RegulationConstant.executeDateMaxAggName).field("regulationExecuteDate");
        aggs.add(regulationExecuteDateMinAgg);
        aggs.add(regulationExecuteDateMaxAgg);
        //单维度桶
        aggs.add(AggregationBuilders.terms(RegulationConstant.validLevelAggName).field("regulationValidLevel"));
        aggs.add(AggregationBuilders.terms(RegulationConstant.postDepartmentAggName).field("regulationDepartment"));
        aggs.add(AggregationBuilders.terms(RegulationConstant.innerRegulationTypeAggName).field("innerCategory"));
        aggs.add(AggregationBuilders.terms(RegulationConstant.innerRegulationControlDepartAggName).field("innerRegulationControlDepart")
                .includeExclude(new IncludeExclude(null,".*分行.*")).size(500));
        return aggs;
    }

    private void setSort(SearchSourceBuilder searchSourceBuilder, int sortObj, RegulationsAggResponse regResponse, SearchFilterParam searchFilterParam) {
        switch (sortObj){
            case 5:
                List<SortBuilder<?>> sortBuilders = customOrderScript(regResponse.getAllSearchLabels(),searchFilterParam);
                searchSourceBuilder.sort(sortBuilders);
                break;
            case 1:
                searchSourceBuilder.sort("regulationPostDate", SortOrder.DESC);
                break;
            case 2:
                searchSourceBuilder.sort("regulationPostDate", SortOrder.ASC);
                break;
            case 3:
                searchSourceBuilder.sort("regulationExecuteDate", SortOrder.DESC);
                break;
            case 4:
                searchSourceBuilder.sort("regulationExecuteDate", SortOrder.ASC);
                break;
            default:
        }
    }

    private List<SortBuilder<?>> customOrderScript(List<LabelDto> allSearchLabels, SearchFilterParam searchFilterParam) {
        List<SortBuilder<?>> sortBuilders = new ArrayList<>();
        //默认按打分
        sortBuilders.add(SortBuilders.scoreSort());
        String validLevel = searchFilterParam.getValidLevel();
        String postDepartment = searchFilterParam.getPostDepartment();
        String innerControlDepart = searchFilterParam.getInnerControlDepart();
        String innerRegType = searchFilterParam.getInnerRegType();
        if(validLevel!=null){
            String s = "if(doc['regulationValidLevel'].size()!=0){if(doc['regulationValidLevel'].value.equals('"+
                    validLevel+
                    "')){return 1}else{return 0}}else{return 0}";
            Script script = new Script(s);
            ScriptSortBuilder order = SortBuilders.scriptSort(script, ScriptSortBuilder.ScriptSortType.NUMBER).order(SortOrder.DESC);
            sortBuilders.add(order);
        }
        if(postDepartment!=null){
            Script script = getScript(postDepartment);
            ScriptSortBuilder order = SortBuilders.scriptSort(script, ScriptSortBuilder.ScriptSortType.NUMBER).order(SortOrder.DESC);
            sortBuilders.add(order);
        }
        if(innerControlDepart!=null){
            String s = "if(doc['innerRegulationControlDepart'].size()!=0){if(doc['innerRegulationControlDepart'].value.equals('"+
                    innerControlDepart+
                    "')){return 1}else{return 0}}else{return 0}";
            Script script = new Script(s);
            ScriptSortBuilder order = SortBuilders.scriptSort(script, ScriptSortBuilder.ScriptSortType.NUMBER).order(SortOrder.DESC);
            sortBuilders.add(order);
        }
        if(innerRegType!=null) {
            String s = "if(doc['innerCategory'].size()!=0){if(doc['innerCategory'].value.equals('" +
                    innerRegType +
                    "')){return 1}else{return 0}}else{return 0}";
            Script script = new Script(s);
            ScriptSortBuilder order = SortBuilders.scriptSort(script, ScriptSortBuilder.ScriptSortType.NUMBER).order(SortOrder.DESC);
            sortBuilders.add(order);
        }
        if(allSearchLabels.size()>0) {
            String [] params = allSearchLabels.stream().map(LabelDto::getLabelId).toArray(String[]::new);
            Map<String,Object> param = new HashMap<>();
            param.put("labelIDs",params);
            //篇章级标签数量
            String regLabelCount = "def count=0;for(labelId in params.labelIDs){if(params._source.regulationLabels!=null){for(regLabel in params._source.regulationLabels){if(regLabel.labelId.equals(labelId)){count++;}}}}return count;";
            Script script = new Script(ScriptType.INLINE, Script.DEFAULT_SCRIPT_LANG, regLabelCount, param);
            ScriptSortBuilder order = SortBuilders.scriptSort(script, ScriptSortBuilder.ScriptSortType.NUMBER).order(SortOrder.DESC);
            sortBuilders.add(order);
            //段落级标签数量
            String regLawLabelCount = "def count=0;for(labelId in params.labelIDs){if(params._source.regulationLaws!=null){for(law in params._source.regulationLaws){if(law.lawLabels!=null){for(lawLabel in law.lawLabels){if(lawLabel.labelId.equals(labelId)){count++;}}}}}}return count;";
            Script script1 = new Script(ScriptType.INLINE, Script.DEFAULT_SCRIPT_LANG, regLawLabelCount, param);
            ScriptSortBuilder order1 = SortBuilders.scriptSort(script1, ScriptSortBuilder.ScriptSortType.NUMBER).order(SortOrder.DESC);
            sortBuilders.add(order1);
        }
        //外规发文机构
        String outDept = "if(doc['regulationDepartment'].size()!=0){if(doc['regulationDepartment'].value.contains('国家外汇管理局') || doc['regulationDepartment'].value.contains('中国银行保险监督管理委员会') || doc['regulationDepartment'].value.contains('中国人民银行')){return 1}else{return 0}}else{return 0}";
        Script script2 = new Script(outDept);
        ScriptSortBuilder order2 = SortBuilders.scriptSort(script2, ScriptSortBuilder.ScriptSortType.NUMBER).order(SortOrder.DESC);
        sortBuilders.add(order2);
        //内规发文机构
        String innerDept = "if(doc['regulationDepartment'].size()!=0){if(doc['regulationDepartment'].value.contains('总行')){return 1}else if(doc['regulationDepartment'].value.contains('分行')){return 0.5}else{return 0.8}}else{return 0}";
        Script script3 = new Script(innerDept);
        ScriptSortBuilder order3 = SortBuilders.scriptSort(script3, ScriptSortBuilder.ScriptSortType.NUMBER).order(SortOrder.DESC);
        sortBuilders.add(order3);
        //执行日期
        sortBuilders.add(SortBuilders.fieldSort("regulationExecuteDate").order(SortOrder.DESC));
        return sortBuilders;
    }

    private Script getScript(String postDepartment) {
        String s;
        if(postDepartment.equals(RegulationConstant.ybjName)){
            String [] list = {"国家金融监督管理总局","中国银行保险监督管理委员会","中国银行业监督管理委员会(已撤销)","中国保险监督管理委员会(已撤销)"};
            s = "if(doc['regulationDepartment'].size()!=0){if(doc['regulationDepartment'].value in "+list+"){return 1}else{return 0}}else{return 0}";
        }else{
            s = "if(doc['regulationDepartment'].size()!=0){if(doc['regulationDepartment'].value.equals('"+
                    postDepartment +
                    "')){return 1}else{return 0}}else{return 0}";
        }
        Script script = new Script(s);
        return script;
    }

    private void setFromSize(SearchSourceBuilder searchSourceBuilder, int pageSize, int pageNum) {
        int esFrom =0;
        if(pageNum > 1){
            esFrom = (pageNum -1)*pageSize;
        }
        searchSourceBuilder.from(esFrom);
        searchSourceBuilder.size(pageSize);
    }

    private Map<String, Object> createCommonQueryBuilder(RegulationsAggRequest request) {
        Map<String,Object> maps = new HashMap<>();
        SearchFilterParam searchFilterParam = request.getSearchFilterParam();
        // 内规总行条件
        BoolQueryBuilder bh = QueryBuilders.boolQuery();
        // 内规分行条件
        BoolQueryBuilder bb = QueryBuilders.boolQuery();
        // 外规条件
        BoolQueryBuilder be = QueryBuilders.boolQuery();
        BoolQueryBuilder boolQueryBuilder = buildCommonQuery(searchFilterParam, bh, bb, be);
        boolean addLabelUsedFlag = false;
        boolean postDeptUsedFlag = false;
        boolean adminDeptUsedFlag = false;

        String searchContent = request.getSearchContent();
        RegulationsAggResponse regulationsAggResponse = new RegulationsAggResponse();
        ArrayList<Object> highLightFields = new ArrayList<>();
        maps.put("builder", boolQueryBuilder);
        maps.put("resp", regulationsAggResponse);
        maps.put("high", highLightFields);

        if(StrUtil.isBlankIfStr(searchContent)){
            regulationsAggResponse.setSearchType(-1);
        }else{
            //1-1.搜索框内容解析
            Map<String, Object> params = SearchWordDealCenter.parseSearchParam(searchContent);
            regulationsAggResponse.setParseParams(params);
            if(params != null && !params.isEmpty()){
                for (Map.Entry<String, Object> entry : params.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();

                    if(RegulationConstant.regTitleFieldName.equals(key)){//按标题查询
                        List<String> titleList = (List<String>) value;
                        for (String s : titleList) {
                            boolQueryBuilder.must(QueryBuilders.regexpQuery("regulationTitle.keyword", ".*"+s+".*"));
                        }
                        regulationsAggResponse.setSearchType(1);
                        highLightFields.add("regulationTitle");
                        break;
                    }else if(RegulationConstant.regContentFieldName.equals(key)){//按正文查询
                        //正文查询时，同时要匹配标题
                        DisMaxQueryBuilder disMaxQueryBuilder = QueryBuilders.disMaxQuery()
                                .add(QueryBuilders.nestedQuery("regulationLaws",QueryBuilders.matchPhraseQuery("regulationLaws.lawContent",value), ScoreMode.Avg))
                                .add(QueryBuilders.nestedQuery("regulationLaws", QueryBuilders.matchQuery("regulationLaws.lawContent",value),ScoreMode.Avg))
                                .add(QueryBuilders.matchPhraseQuery("regulationContent",value).boost(2.0f))
                                .add(QueryBuilders.matchQuery("regulationContent",value).boost(0.1f))
                                .add(QueryBuilders.matchQuery("regulationTitle",value).boost(100.0f))
                                .tieBreaker(0.7f);
                        boolQueryBuilder.must(disMaxQueryBuilder);
                        regulationsAggResponse.setSearchType(2);
                        highLightFields.add("regulationTitle");
                        highLightFields.add("regulationContent");
                        highLightFields.add("regulationLaws.lawContent");
                        maps.put("minScore",1);
                        break;
                    }else if(RegulationConstant.regLabelsFieldName.equals(key)){//按标签查询
                        regulationsAggResponse.setSearchType(3);
                        // 多标签求交集
                        List<LabelDto> labelDtos = new ArrayList<>();
                        labelDtos.addAll((List<LabelDto>) value);
                        //二次选择的标签
                        if(searchFilterParam!=null){
                            List<LabelDto> list = searchFilterParam.getAddLabels();
                            if(list != null && list.size() > 0){
                                addLabelUsedFlag = true;
                                labelDtos.addAll(list);
                            }
                        }
                        regulationsAggResponse.setAllSearchLabels(labelDtos);
                        //只查询直系子标签，再用本级标签及其子标签查询法规
                        List<String> pIds = labelDtos.stream().map(LabelDto::getLabelId).distinct().collect(Collectors.toList());
                        addParallelLabelNestedQuery(boolQueryBuilder, pIds);
                    }else {//其它属性查
                        //发文日期
                        if(RegulationConstant.regPostDateStartFieldName.equals(key)){
                            boolQueryBuilder.must(QueryBuilders.rangeQuery("regulationPostDate").gte(value));
                            //如果map中不包含截止日期
                            if(!params.containsKey(RegulationConstant.regPostDateEndFieldName)){
                                String toDate = "";
                                String [] split = value.toString().split("-");
                                if(split.length == 1){
                                    toDate = value+"||+1y";
                                }else if(split.length == 2){
                                    toDate = value+"||+1M";
                                }else{
                                    toDate = value+"||+1d";
                                }
                                boolQueryBuilder.must(QueryBuilders.rangeQuery("regulationPostDate").lte(toDate));
                            }
                            highLightFields.add("regulationPostDate");
                        }
                    }
                    if(RegulationConstant.regPostDateEndFieldName.equals(key)){
                        boolQueryBuilder.must(QueryBuilders.rangeQuery("regulationPostDate").lte(value));
                        if(!highLightFields.contains("regulationPostDate")){
                            highLightFields.add("regulationPostDate");
                        }
                    }
                    //发文部门
                    if(RegulationConstant.regOutPostDeptFieldName.equals(key)){
                        if(searchFilterParam != null){
                            String postDepartment = searchFilterParam.getPostDepartment();
                            if(!StrUtil.isEmpty(postDepartment)){
                                postDeptUsedFlag = true;
                                value = postDepartment;
                            }
                        }
                        if(value.equals(RegulationConstant.ybjName)
                                ||value.equals("中国银行保险监督管理委员会")
                        ||value.equals("国家金融监督管理总局")){
                            String [] list = {"国家金融监督管理总局","中国银行保险监督管理委员会","中国银行业监督管理委员会(已撤销)","中国保险监督管理委员会(已撤销)"};
                            boolQueryBuilder.must(QueryBuilders.termsQuery("regulationDepartment", list));
                        }else{
                            boolQueryBuilder.must(QueryBuilders.termQuery("regulationDepartment",value));
                        }
                        highLightFields.add("regulationDepartment");
                    }
                    //内规-管理部门
                    if(RegulationConstant.regInnerControlDepartFieldName.equals(key)){
                        if(searchFilterParam != null){
                            String innerControlDepart = searchFilterParam.getInnerControlDepart();
                            if(!StrUtil.isEmpty(innerControlDepart)){
                                adminDeptUsedFlag = true;
                                value = innerControlDepart;
                            }
                        }
                        boolQueryBuilder.must(QueryBuilders.wildcardQuery("innerRegulationControlDepart", "*"+value+"*"));
                        highLightFields.add("innerRegulationControlDepart");
                    }
                    //发文编号
                    if(RegulationConstant.regNumberFieldName.equals(key)){
                        boolQueryBuilder.must(QueryBuilders
                                .regexpQuery("regulationNumber.keyword", ".*"+value.toString().replace("号","")+".*"));
                        highLightFields.add("regulationNumber");
                    }
                }
            }
        }
        //add_labels、发文部门、管理部门三个属性如果不包含在语义解析条件中，需要补充出来
        if(searchFilterParam != null){
            Integer innerOutType = searchFilterParam.getInnerOutType();
            String postDepartment = searchFilterParam.getPostDepartment();
            if(!StrUtil.isEmpty(postDepartment)&&!postDeptUsedFlag){
                if(postDepartment.equals(RegulationConstant.ybjName)
                        ||postDepartment.equals("中国银行保险监督管理委员会")
                        ||postDepartment.equals("国家金融监督管理总局")) {
                    String[] list = {"国家金融监督管理总局", "中国银行保险监督管理委员会", "中国银行业监督管理委员会(已撤销)", "中国保险监督管理委员会(已撤销)"};
                    if (innerOutType != null && (innerOutType == ChooseType.EXTERNAL.getCode()
                            || innerOutType == ChooseType.EXTERNAL_INNER_HEAD.getCode()
                            || innerOutType == ChooseType.EXTERNAL_INNER_BRANCH.getCode())) {
                        be.must(QueryBuilders.termsQuery("regulationDepartment", list));
                    } else {
                        boolQueryBuilder.must(QueryBuilders.termsQuery("regulationDepartment", list));
                    }
                }else{
                    if (innerOutType != null && (innerOutType == ChooseType.EXTERNAL.getCode()
                            || innerOutType == ChooseType.EXTERNAL_INNER_HEAD.getCode()
                            || innerOutType == ChooseType.EXTERNAL_INNER_BRANCH.getCode())) {
                        be.must(QueryBuilders.termQuery("regulationDepartment", postDepartment));
                    }else{
                        boolQueryBuilder.must(QueryBuilders.termQuery("regulationDepartment", postDepartment));
                    }
                }
            }
            List<LabelDto> addLabels = searchFilterParam.getAddLabels();
            List<LabelDto> labelDtoList = new ArrayList<>();
            if(addLabels != null && addLabels.size() > 0&&!addLabelUsedFlag){
                labelDtoList.addAll(addLabels);
                regulationsAggResponse.setSearchType(3);
                regulationsAggResponse.setAllSearchLabels(labelDtoList);
                List<String> pIds = labelDtoList.stream().map(item -> item.getLabelId()).collect(Collectors.toList());
                addParallelLabelNestedQuery(boolQueryBuilder, pIds);
            }
            String innerControlDepart = searchFilterParam.getInnerControlDepart();
            if(!StrUtil.isEmpty(innerControlDepart)&&!adminDeptUsedFlag){
                if(innerOutType!=null&&(innerOutType == ChooseType.INNER.getCode()
                ||innerOutType==ChooseType.ALL.getCode()
                ||innerOutType==ChooseType.EXTERNAL_INNER_BRANCH.getCode())){
                    bh.filter(QueryBuilders.wildcardQuery("innerRegulationControlDepart", "*"+innerControlDepart+"*"));
                }else{
                    boolQueryBuilder.must(QueryBuilders.wildcardQuery("innerRegulationControlDepart", "*"+innerControlDepart+"*"));
                }
            }
        }
        return maps;
    }
    private void addParallelLabelNestedQuery(BoolQueryBuilder boolQueryBuilder, List<String> pIds) {
        List<LabelLink> labelLinks = SearchFieldDataCenter.getInstance().getLabelLinks();
        //查询父子关系
        Map<String,List<LabelLink>> collect = labelLinks.parallelStream().filter(e -> pIds.contains(e.getFatherLabelId()))
                .collect(Collectors.groupingBy(LabelLink::getFatherLabelId));
        List<String> collect2 = collect.keySet().stream().collect(Collectors.toList());
        boolean b = pIds.removeAll(collect2);
        if(collect2.size() == 0||b){
            for (String id : pIds) {
                collect.put(id,null);
            }
        }
        for (String s : collect.keySet()) {
            List<String> str = new ArrayList<>();
            str.add(s);
            List<LabelLink> labelLinks1 = collect.get(s);
            if(labelLinks1!=null){
                labelLinks1.parallelStream().map(e->e.getSonLabelId()).collect(Collectors.toList()).forEach(str::add);
            }
            BoolQueryBuilder boolQueryBuilder1 = QueryBuilders.boolQuery();
            boolQueryBuilder1.should(QueryBuilders.nestedQuery("regulationLabels",QueryBuilders.termsQuery("regulationLabels.labelId",str), ScoreMode.None));
            boolQueryBuilder1.should(QueryBuilders.nestedQuery("regulationLaws.lawLabels",QueryBuilders.termsQuery("regulationLaws.lawLabels.labelId",str), ScoreMode.None));
            boolQueryBuilder.must(boolQueryBuilder1);

        }
    }

     private BoolQueryBuilder buildCommonQuery(SearchFilterParam searchFilterParam, BoolQueryBuilder bh, BoolQueryBuilder bb, BoolQueryBuilder be) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.mustNot(QueryBuilders.termsQuery("valid", RegulationConstant.INVALID));
        if(searchFilterParam != null){
            Integer innerOutType = searchFilterParam.getInnerOutType();
            if(innerOutType!=null && innerOutType!=0){
                if(innerOutType == ChooseType.INNER_HEAD.getCode()){// 总行、理财、金租
                    bh.should(QueryBuilders.prefixQuery("innerRegulationControlDepart", "总行"));
                    bh.should(QueryBuilders.prefixQuery("innerRegulationControlDepart", "华夏理财"));
                    bh.should(QueryBuilders.prefixQuery("innerRegulationControlDepart", "华夏金融租赁"));
                    boolQueryBuilder.must(bh);
                }else if(innerOutType == ChooseType.INNER_BRANCH.getCode()){// 分行内规
                    String userDept = getUserDept();
                    if(userDept.contains("分行")){
                        bb.must(QueryBuilders.wildcardQuery("innerRegulationControlDepart", userDept+"*"));
                    }else{
                        bb.must(QueryBuilders.wildcardQuery("innerRegulationControlDepart", "*分行*"));
                    }
                    bb.filter(QueryBuilders.termQuery("regulationType", ChooseType.INNER.getCode()));
                    boolQueryBuilder.must(bb);
                }else if(innerOutType == ChooseType.INNER.getCode()){// 内规
                    boolQueryBuilder.filter(QueryBuilders.termQuery("regulationType", ChooseType.INNER.getCode()));
                    String userDept = getUserDept();
                    if(userDept.contains("分行")){
                        // 分行用户可以查看：总行内规 + 自己分行内规
                        BoolQueryBuilder innerQuery = QueryBuilders.boolQuery();
                        // 总行条件
                        innerQuery.should(QueryBuilders.prefixQuery("innerRegulationControlDepart", "总行"));
                        innerQuery.should(QueryBuilders.prefixQuery("innerRegulationControlDepart", "华夏理财"));
                        innerQuery.should(QueryBuilders.prefixQuery("innerRegulationControlDepart", "华夏金融租赁"));
                        // 自己分行条件
                        innerQuery.should(QueryBuilders.wildcardQuery("innerRegulationControlDepart", userDept+"*"));
                        boolQueryBuilder.must(innerQuery);
                    }
                }else if(innerOutType == ChooseType.EXTERNAL_INNER_HEAD.getCode()){// 外规+总行
                    // 外规条件
                    be.filter(QueryBuilders.termQuery("regulationType", ChooseType.EXTERNAL.getCode()));
                    // 总行条件（与INNER_HEAD相同的逻辑）
                    bh.should(QueryBuilders.prefixQuery("innerRegulationControlDepart", "总行"));
                    bh.should(QueryBuilders.prefixQuery("innerRegulationControlDepart", "华夏理财"));
                    bh.should(QueryBuilders.prefixQuery("innerRegulationControlDepart", "华夏金融租赁"));
                    // 组合条件：外规 OR 总行
                    BoolQueryBuilder combinedQuery = QueryBuilders.boolQuery();
                    combinedQuery.should(be);
                    combinedQuery.should(bh);
                    boolQueryBuilder.must(combinedQuery);
                }else if(innerOutType == ChooseType.EXTERNAL_INNER_BRANCH.getCode()) {// 外规+分行
                    // 外规条件
                    be.filter(QueryBuilders.termQuery("regulationType", ChooseType.EXTERNAL.getCode()));
                    // 分行条件（与INNER_BRANCH相同的逻辑）
                    String userDept = getUserDept();
                    if(userDept.contains("分行")){
                        bb.must(QueryBuilders.wildcardQuery("innerRegulationControlDepart", userDept+"*"));
                    }else{
                        bb.must(QueryBuilders.wildcardQuery("innerRegulationControlDepart", "*分行*"));
                    }
                    bb.filter(QueryBuilders.termQuery("regulationType", ChooseType.INNER.getCode()));
                    // 组合条件：外规 OR 分行
                    BoolQueryBuilder combinedQuery = QueryBuilders.boolQuery();
                    combinedQuery.should(be);
                    combinedQuery.should(bb);
                    boolQueryBuilder.must(combinedQuery);
                }else if(innerOutType == ChooseType.EXTERNAL.getCode()){
                    boolQueryBuilder.filter(QueryBuilders.termQuery("regulationType", ChooseType.EXTERNAL.getCode()));
                }
            }else{// 查询所有：外规+内规总行+自己分行
                String userDept = getUserDept();
                BoolQueryBuilder allQuery = QueryBuilders.boolQuery();
                
                // 外规条件
                allQuery.should(QueryBuilders.termQuery("regulationType", ChooseType.EXTERNAL.getCode()));
                
                // 内规总行条件
                BoolQueryBuilder headQuery = QueryBuilders.boolQuery();
                headQuery.must(QueryBuilders.termQuery("regulationType", ChooseType.INNER.getCode()));
                headQuery.should(QueryBuilders.prefixQuery("innerRegulationControlDepart", "总行"));
                headQuery.should(QueryBuilders.prefixQuery("innerRegulationControlDepart", "华夏理财"));
                headQuery.should(QueryBuilders.prefixQuery("innerRegulationControlDepart", "华夏金融租赁"));
                allQuery.should(headQuery);
                
                // 自己分行内规条件（如果是分行用户）
                if(userDept.contains("分行")){
                    BoolQueryBuilder branchQuery = QueryBuilders.boolQuery();
                    branchQuery.must(QueryBuilders.termQuery("regulationType", ChooseType.INNER.getCode()));
                    branchQuery.must(QueryBuilders.wildcardQuery("innerRegulationControlDepart", userDept+"*"));
                    allQuery.should(branchQuery);
                }
                
                boolQueryBuilder.must(allQuery);
            }
        }
        return boolQueryBuilder;
    }

    private String getUserDept(){
        // 正式环境是通过从缓存中获取当前登录用户的部门信息
        return "xx分行";
    }
}
