package life.ljs.hg.regulations.service;

import cn.hutool.core.util.StrUtil;
import life.ljs.hg.regulations.dto.LabelDto;
import life.ljs.hg.regulations.dto.LabelLink;
import life.ljs.hg.regulations.dto.PostDepartDto;
import life.ljs.hg.regulations.dto.RegTypeDto;
import org.apache.lucene.analysis.miscellaneous.KeepWordFilter;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/7/24
 **/
public class SearchFieldDataCenter {
    private List<LabelDto> labelList = new CopyOnWriteArrayList<>();
    private List<LabelLink> labelLinks = new CopyOnWriteArrayList<>();
    private List<PostDepartDto> outPostDeptList = new CopyOnWriteArrayList<>();
    private List<RegTypeDto> regTypeList = new CopyOnWriteArrayList<>();
    /**
     * 分行及子公司
     */
    private List<String> adminDeptList = new CopyOnWriteArrayList<>();
    /**
     * 内部管理部门
     */
    private List<String> innerPostDeptList = new CopyOnWriteArrayList<>();

    private static final SearchFieldDataCenter instance = new SearchFieldDataCenter();
    public static SearchFieldDataCenter getInstance(){
        return instance;
    }

    public void addLabel(String labelId,
                         String fullName,
                         String firstLevel,
                         String ... synonymsName){
        if(!labelList.isEmpty()){
            for (LabelDto labelDto : labelList) {
                if(labelDto.getLabelId().equals(labelId)){
                    List<String> synonyms = labelDto.getSynonyms();
                    if(synonyms != null && synonyms.size() > 0){
                        for (String s : synonymsName) {
                            if(!synonyms.stream().anyMatch(o->s.equals(o))){
                                synonyms.add(s);
                            }
                        }
                    }else{
                        labelDto.setSynonyms(Arrays.stream(synonymsName).collect(Collectors.toList()));
                    }
                    return;
                }
            }
        }
        LabelDto labelDto = new LabelDto();
        labelDto.setLabelName(fullName);
        labelDto.setLabelId(labelId);
        labelDto.setFirstLevel(firstLevel);
        if(synonymsName.length > 0){
            List<String> nickNames = new ArrayList<>();
            for (String s : synonymsName) {
                nickNames.add(s);
            }
            labelDto.setSynonyms(nickNames);
        }
        labelList.add(labelDto);
    }

    public void addLinks(LabelLink labelLink){
        if (labelLink == null) {
            return;
        }
        if(labelLinks.contains(labelLink)){
            return;
        }
        labelLinks.add(labelLink);
    }

    public List<LabelDto> getSonLabelByLabelId(String labelId){
        List<LabelDto> result = new ArrayList<>();
        for (LabelLink labelLink : labelLinks) {
            if(labelLink.getFatherLabelId().equals(labelId)){
                LabelDto label = getLabelById(labelLink.getSonLabelId());
                if(label != null){
                    result.add(label);
                }
            }
        }
        return result;
    }
    public List<LabelDto> getParentLabelByLabelId(String labelId){
        List<LabelDto> result = new ArrayList<>();
        for (LabelLink labelLink : labelLinks) {
            if(labelLink.getSonLabelId().equals(labelId)){
                LabelDto label = getLabelById(labelLink.getFatherLabelId());
                if(label != null){
                    result.add(label);
                }
            }
        }
        return result;
    }
    public LabelDto getLabelById(String labelId){
        for (LabelDto labelDto : labelList) {
            if(labelDto.getLabelId().equals(labelId)){
                return labelDto;
            }
        }
        return null;
    }
    public List<LabelLink> getLabelLinks(){
        return labelLinks;
    }
    public List<LabelDto> getLabelList(){
        return labelList;
    }

    public void addOutPostDept(String outDeptName,String ... synonymsName){
        PostDepartDto postDepartDto = new PostDepartDto();
        postDepartDto.setDeptName(outDeptName);
        if(synonymsName !=null && synonymsName.length > 0){
            postDepartDto.setSynonyms(Arrays.stream(synonymsName).collect(Collectors.toList()));
        }
        if(!outPostDeptList.contains(postDepartDto)){
            outPostDeptList.add(postDepartDto);
        }
    }

    public List<PostDepartDto> getOutPostDeptList(){
        return outPostDeptList;
    }

    public String getOutPostDeptFullName(String keyword){
        for (PostDepartDto postDepartDto : outPostDeptList) {
            List<String> synonyms = postDepartDto.getSynonyms();
            boolean b = false;
            if(synonyms != null && synonyms.size() > 0){
                b = synonyms.stream().anyMatch(str -> keyword.equals(str));
            }
            if(keyword.equals(postDepartDto.getDeptName()) || b){
                return postDepartDto.getDeptName();
            }
        }
        return null;
    }

    public void addRegType(){
        RegTypeDto regTypeDto = new RegTypeDto();
        regTypeDto.setType(1);
        regTypeDto.setTypeName("外规");
        regTypeDto.setSynonyms(Arrays.asList("外部制度","外部法规"));
        regTypeList.add(regTypeDto);

        RegTypeDto dto = new RegTypeDto();
        dto.setType(2);
        dto.setTypeName("内规");
        dto.setSynonyms(Arrays.asList("内部制度","内部法规"));
        regTypeList.add(dto);
    }

    public int getRegType(String keyword){
        for (RegTypeDto regTypeDto : regTypeList) {
            int type = regTypeDto.getType();
            boolean b = regTypeDto.getSynonyms().stream().anyMatch(str -> keyword.equals(str));
            if(keyword.equals(regTypeDto.getTypeName()) || b){
                return type;
            }
        }
        return -1;
    }

    public void addInnerPostDept(String innerPostDept){
        if(!innerPostDeptList.contains(innerPostDept)){
            innerPostDeptList.add(innerPostDept);
        }
    }
    public String getInnerPostDeptName(String keyword){
        if(StrUtil.isEmpty(keyword)){
            return null;
        }
        if(innerPostDeptList.contains(keyword)){
            return keyword;
        }
        return null;
    }
    public void addAdminDept(String adminDept){
        if(!adminDeptList.contains(adminDept)){
            adminDeptList.add(adminDept);
        }
    }
}
