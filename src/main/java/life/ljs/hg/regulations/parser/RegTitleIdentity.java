package life.ljs.hg.regulations.parser;

import life.ljs.hg.regulations.constants.RegulationConstant;

import java.util.*;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/8/13
 **/
public class RegTitleIdentity implements Identify{
    @Override
    public Map<String, Object> identity(String[] searchWords) {
        Map<String,Object> result = new HashMap<>();
        List<String> titleList = new ArrayList<>();
        for (String searchWord : searchWords) {
            boolean b1 = searchWord.contains("标题:");
            boolean b2 = searchWord.contains("标题：");
            boolean b3 = searchWord.contains("正文：");
            boolean b4 = searchWord.contains("正文:");
            if(b3||b4){
                String trim = searchWord.replace("正文：", "")
                        .replace("正文:", "")
                        .trim();
                result.put(RegulationConstant.regContentFieldName, trim);
                break;
            }
            String title = null;
            if(b1){
                title = searchWord.replace("标题:", "").trim();
            }
            if(b2){
                title = searchWord.replace("标题：", "").trim();
            }
            if(b1||b2){
                titleList.add(title);
            }else{
                String [] titleEnds = {
                        "通知","办法","方案","预案","规程","规则",
                        "流程","细则","制度","规范","规定","批复",
                        "测试","分册","意见","计划","指引","公告",
                        "意见","批复","报告","决定","函","条例",
                        "通报","法","修订","修正","答复","解释",
                        "提示","通则"
                };
                boolean b = Arrays.stream(titleEnds).anyMatch(str -> searchWord.endsWith(str));
                if(b){
                    titleList.add(searchWord);
                }
            }
            if (titleList.size() >0){
                result.put(RegulationConstant.regTitleFieldName, titleList);
            }
        }
        return result;
    }
}
