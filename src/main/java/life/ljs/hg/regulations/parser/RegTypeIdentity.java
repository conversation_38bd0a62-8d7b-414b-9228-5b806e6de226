package life.ljs.hg.regulations.parser;

import life.ljs.hg.regulations.constants.RegulationConstant;
import life.ljs.hg.regulations.service.SearchFieldDataCenter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/8/13
 **/
public class RegTypeIdentity implements Identify {

    @Override
    public Map<String, Object> identity(String[] searchWords) {
        Map<String,Object> typeMap = new HashMap<>();
        SearchFieldDataCenter instance = SearchFieldDataCenter.getInstance();
        for (String searchWord : searchWords) {
            int regType = instance.getRegType(searchWord);
            if(regType != -1){
                typeMap.put(RegulationConstant.regTypeFieldName, regType+"");
                return typeMap;
            }
        }
        return null;
    }
}
