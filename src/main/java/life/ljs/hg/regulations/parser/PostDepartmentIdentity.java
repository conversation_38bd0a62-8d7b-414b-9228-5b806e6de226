package life.ljs.hg.regulations.parser;

import cn.hutool.core.util.StrUtil;
import life.ljs.hg.regulations.constants.RegulationConstant;
import life.ljs.hg.regulations.service.SearchFieldDataCenter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/8/13
 **/
public class PostDepartmentIdentity implements Identify{
    @Override
    public Map<String, Object> identity(String[] searchWords) {
        SearchFieldDataCenter instance = SearchFieldDataCenter.getInstance();
        Map<String,Object> outPostMap = new HashMap<>();
        for (String searchWord : searchWords) {
            String outPostDeptFullName;
            if(searchWord.equals("银监会")){
                outPostDeptFullName = "中国银行业监督管理委员会(已撤销)";
            }else if(searchWord.equals("保监会")){
                outPostDeptFullName = "中国保险监督管理委员会(已撤销)";
            }else{
                outPostDeptFullName = instance.getOutPostDeptFullName(searchWord);
            }
            if(!StrUtil.isEmpty(outPostDeptFullName)){
                outPostMap.put(RegulationConstant.regOutPostDeptFieldName, outPostDeptFullName);
                return outPostMap;
            }
        }
        return null;
    }
}
