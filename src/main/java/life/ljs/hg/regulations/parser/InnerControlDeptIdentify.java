package life.ljs.hg.regulations.parser;

import cn.hutool.core.util.StrUtil;
import life.ljs.hg.regulations.constants.RegulationConstant;
import life.ljs.hg.regulations.service.SearchFieldDataCenter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/7/24
 **/
public class InnerControlDeptIdentify implements Identify{
    @Override
    public Map<String, Object> identity(String[] searchWords) {
        SearchFieldDataCenter instance = SearchFieldDataCenter.getInstance();
        Map<String,Object> adminDeptMap = new HashMap<>();
        for (String searchWord : searchWords) {
            String innerPostDeptName = instance.getInnerPostDeptName(searchWord);
            if(StrUtil.isEmpty(innerPostDeptName)){
                adminDeptMap.put(RegulationConstant.regInnerControlDepartFieldName, innerPostDeptName);
                return adminDeptMap;
            }
        }
        return null;
    }
}
