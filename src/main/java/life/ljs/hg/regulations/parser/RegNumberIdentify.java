package life.ljs.hg.regulations.parser;

import life.ljs.hg.regulations.constants.RegulationConstant;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/8/13
 **/
public class RegNumberIdentify implements Identify{
    public static final String format_1 = "\\d+\\u53f7$";
    public static final String format_2 = "[\\u4e00-\\u9fa5]+\\u53f7";
    Pattern p1 = Pattern.compile(format_1);
    Pattern p2 = Pattern.compile(format_2);
    @Override
    public Map<String, Object> identity(String[] searchWords) {
        Map<String,Object> regNumberMap = new HashMap<>();
        for (String searchWord : searchWords) {
            boolean b1 = p1.matcher(searchWord).find();
            boolean b2 = p2.matcher(searchWord).find();
            if(b1||b2){
                regNumberMap.put(RegulationConstant.regNumberFieldName, searchWord);
                return regNumberMap;
            }
        }
        return null;
    }
}
