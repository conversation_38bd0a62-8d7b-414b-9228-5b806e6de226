package life.ljs.hg.regulations.parser;

import life.ljs.hg.regulations.constants.RegulationConstant;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/7/24
 **/
public class DateTimeIdentify implements Identify{
    /**
     * 19年12月、2019年6月
     */
    public static String format_1 = "^\\d{2,4}\\u5e74+\\d{1,2}\\u6708$";
    Pattern pattern1 = Pattern.compile(format_1);
    /**
     * 2019年，2019
     */
    public static String format_2 = "^\\d{4}\\u5e74?$";
    Pattern pattern2 = Pattern.compile(format_2);
    /**
     * 19年
     */
    public static String format_3 = "^\\d{2}\\u5e74$";
    Pattern pattern3 = Pattern.compile(format_3);

    @Override
    public Map<String, Object> identity(String[] searchWords) {
        Map<String,Object> dataMap = new HashMap<>();
        for (String searchWord : searchWords) {
            if(searchWord.contains("至")){//日期范围
                String[] zhiArray = searchWord.split("至");
                if(zhiArray.length == 2){
                    String start = zhiArray[0];
                    String end = zhiArray[1];
                    if(start.matches(format_2) && end.matches(format_2)){//只有年份
                        if(start.contains(RegulationConstant.yearCN)){
                            start = start.replace(RegulationConstant.yearCN, "");
                            end = end.replace(RegulationConstant.yearCN,"");
                        }
                        dataMap.put(RegulationConstant.regPostDateStartFieldName, start);
                        dataMap.put(RegulationConstant.regPostDateEndFieldName, end);
                        return dataMap;
                    }
                    if(start.matches(format_1) && end.matches(format_1)){//年月
                        String[] yearSplitArray = start.split(RegulationConstant.yearCN);
                        String startYear = yearSplitArray[0];
                        String startMonth = yearSplitArray[1].replace(RegulationConstant.monthCN, "");
                        startYear = completeYear(startYear);
                        startMonth = completeMonth(startMonth);
                        start = startYear + RegulationConstant.dateSeparator + startMonth;
                        //end
                        yearSplitArray = end.split(RegulationConstant.yearCN);
                        String endYear = yearSplitArray[0];
                        String endMonth = yearSplitArray[1].replace(RegulationConstant.monthCN, "");
                        endYear = completeYear(endYear);
                        endMonth = completeMonth(endMonth);
                        end = endYear + RegulationConstant.dateSeparator + endMonth;
                        dataMap.put(RegulationConstant.regPostDateStartFieldName, start);
                        dataMap.put(RegulationConstant.regPostDateEndFieldName, end);

                        return dataMap;
                    }
                }
            }else{//单日期匹配
                Matcher matcher3 = pattern3.matcher(searchWord);
                if(matcher3.find()){
                    String group = matcher3.group();
                    group = completeYear(group.replace(RegulationConstant.yearCN, ""));
                    dataMap.put(RegulationConstant.regPostDateStartFieldName, group);
                }
                Matcher matcher2 = pattern2.matcher(searchWord);
                if(matcher2.find()){
                    String group = matcher2.group();
                    if(group.contains(RegulationConstant.yearCN)){
                        group = group.replace(RegulationConstant.yearCN, "");
                    }
                    dataMap.put(RegulationConstant.regPostDateStartFieldName, group);
                }
                Matcher matcher1 = pattern1.matcher(searchWord);
                if(matcher1.find()){
                    String group = matcher1.group();
                    String[] split = group.split(RegulationConstant.yearCN);
                    String s = split[0];
                    String s1 = completeYear(s);
                    String s2 = completeMonth(split[1].replace(RegulationConstant.monthCN, ""));
                    dataMap.put(RegulationConstant.regPostDateStartFieldName, s1 + RegulationConstant.dateSeparator + s2);
                }
                return dataMap;
            }
        }
        return dataMap;
    }
    public String completeYear(String year){
        if(year.length() == 2){
            year = RegulationConstant.yearPrefix + year;
        }
        return year;
    }
    public String completeMonth(String month){
        if(month.length() == 1){
            month = RegulationConstant.monthPrefix + month;
        }
        return month;
    }

}
