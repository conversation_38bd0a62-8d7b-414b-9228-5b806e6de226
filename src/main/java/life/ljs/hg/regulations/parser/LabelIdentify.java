package life.ljs.hg.regulations.parser;

import life.ljs.hg.regulations.constants.RegulationConstant;
import life.ljs.hg.regulations.dto.LabelDto;
import life.ljs.hg.regulations.service.SearchFieldDataCenter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/8/13
 **/
public class LabelIdentify implements Identify{
    @Override
    public Map<String, Object> identity(String[] searchWords) {
        Map<String,Object> labelMap = new HashMap<>();
        List<LabelDto> labels = new ArrayList<>();
        List<LabelDto> labelList = SearchFieldDataCenter.getInstance().getLabelList();
        for (LabelDto labelDto : labelList) {
            for (String searchWord : searchWords) {
                if(labelDto.getLabelName().equals(searchWord) || labelDto.getSynonyms().contains(searchWord)){
                    labels.add(labelDto);
                }
            }
        }
        if(labels.size() > 0){
            labelMap.put(RegulationConstant.regLabelsFieldName, labels);
            return labelMap;
        }
        return null;
    }
}
