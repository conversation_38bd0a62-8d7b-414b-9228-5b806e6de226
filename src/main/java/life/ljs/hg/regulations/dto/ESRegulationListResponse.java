package life.ljs.hg.regulations.dto;

import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/7/22
 **/
@Data
public class ESRegulationListResponse {
    private String id;
    private String regulationTitle;
    private Integer regulationType;
    private String regulationNumber;
    private String regulationPostDate;
    private String regulationExecuteDate;
    // 内规管理部门
    private String innerRegulationControlDepart;
    private List<String> regulationDepartment;

    private Integer type;

    //按标签查询时，独有的返回参数
    private List<RegulationLaw> regulationLaws;
    private List<Label> regulationLabels;
    private Map<String, List<String>> highlightMap = new HashMap<>();
    private String content;
}
