package life.ljs.hg.regulations.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/7/22
 **/
@Data
public class SearchFilterParam {
    private List<LabelDto> addLabels;
    private Integer innerOutType;
    private String validLevel;
    private String postDepartment;
    private String innerControlDepart;
    private String innerRegType;
    private String executeMinDate;
    private String executeMaxDate;
    private String regulationPostDateMin;
    private String regulationPostDateMax;
    private List<String> validLevels;
    private List<String> postDepartments;
    private List<String> innerControlDeparts;
    // 机构
    private String org;
    // 主管部门
    private String department;
}
