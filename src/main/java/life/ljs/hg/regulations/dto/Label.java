package life.ljs.hg.regulations.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/7/22
 **/
@Data
public class Label {
    private String labelId;
    private String labelName;
    private String labelCategory;
    private String lawLabelId;
    private String firstLevel;
    private List<Label> parentLabel;

    @Override
    public boolean equals(Object o){
        if(this == o){
            return true;
        }
        if(o == null || getClass() != o.getClass()){
            return false;
        }
        Label label = (Label) o;
        return labelId.equals(label.labelId) && labelName.equals(label.labelName);
    }
    @Override
    public int hashCode(){
        return Objects.hash(labelId, labelName);
    }

}
