package life.ljs.hg.regulations.dto;

import lombok.Data;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/7/22
 **/
@Data
public class RegulationLaw {
    private String lawId;
    private String title;
    private String lawTitle;
    private String lawContent;
    private String highlightLawContent;
    //章
    private String character;
    //节
    private String section;
    //条款号
    private String lawNumber;
    // 该法条是否包含复杂拆分结构：0-不包含  1-包含
    private int lawType;
    private Label [] lawLabels;
    private String lawLabelsContent;
}
