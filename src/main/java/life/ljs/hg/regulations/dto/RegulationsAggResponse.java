package life.ljs.hg.regulations.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/7/22
 **/
@Data
public class RegulationsAggResponse {
    private long totalCount;
    private List<ESRegulationListResponse> results;
    private Map<String,Object> aggs;
    private int searchType;
    private Map<String,Object> parseParams;
    // 合并语义解析和二次过滤选择的标签合集
    private List<LabelDto> allSearchLabels;
    // 标签集
    private List<LevelLabel> labels;
}
